# 🚀 Hướng dẫn Cài đặt Nhanh

## Phương pháp 1: Sử dụng script tự động (Khuyến nghị)

### 1. Chạy script cài đặt
```bash
python run_app.py
```

Script sẽ tự động:
- ✅ Kiểm tra phiên bản Python
- ✅ Cài đặt dependencies
- ✅ Kiểm tra file cấu hình
- ✅ Khởi chạy ứng dụng

### 2. C<PERSON>u hình thông tin kết nối
```bash
# Copy file cấu hình mẫu
cp .env.example .env

# Chỉnh sửa file .env với thông tin thật
notepad .env  # Windows
nano .env     # Linux/Mac
```

**Cập nhật các thông tin sau trong file .env:**
```env
MYSQL_PASSWORD=your_real_password_here
OPENAI_API_KEY=your_openai_api_key_here
```

---

## Phương pháp 2: <PERSON><PERSON>i đặt thủ công

### 1. <PERSON><PERSON><PERSON> tra yêu cầu hệ thống
```bash
python --version  # Cần Python 3.8+
```

### 2. Cài đặt dependencies
```bash
pip install -r requirements.txt
```

### 3. Cấu hình
```bash
cp .env.example .env
# Chỉnh sửa .env với thông tin thật
```

### 4. Chạy ứng dụng
```bash
streamlit run app.py
```

---

## 🔧 Xử lý sự cố thường gặp

### Lỗi: "ModuleNotFoundError"
```bash
pip install --upgrade pip
pip install -r requirements.txt
```

### Lỗi: "Connection refused" (MySQL)
- ✅ Kiểm tra MySQL server đang chạy
- ✅ Kiểm tra thông tin kết nối trong .env
- ✅ Kiểm tra firewall/network

### Lỗi: "Invalid API key" (OpenAI)
- ✅ Kiểm tra API key trong .env
- ✅ Kiểm tra base URL
- ✅ Kiểm tra quota API

### Lỗi: "Port already in use"
```bash
# Chạy trên port khác
streamlit run app.py --server.port 8502
```

---

## 📱 Truy cập ứng dụng

Sau khi khởi chạy thành công:
- 🌐 **URL**: http://localhost:8501
- 📱 **Mobile**: Sử dụng IP máy tính thay localhost
- 🔄 **Refresh**: F5 hoặc Ctrl+R

---

## 🎯 Bước đầu sử dụng

1. **Kiểm tra kết nối**: Vào tab "Query & Chat" → "Xem Schema Database"
2. **Thử nghiệm**: Chọn cấu hình mẫu "thu_chi" → Click "Thực thi"
3. **Thêm cấu hình**: Vào tab "Admin Config" → "Thêm Cấu hình"

---

## 📞 Hỗ trợ

Nếu gặp vấn đề:
1. 📋 Kiểm tra log trong terminal
2. 🔍 Xem thông báo lỗi trên giao diện
3. 📖 Đọc file README.md để biết thêm chi tiết
