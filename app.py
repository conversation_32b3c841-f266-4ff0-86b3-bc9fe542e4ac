"""
Ứng dụng Streamlit tích hợp OpenAI API và MySQL
Quản lý cấu hình động cho phân tích dữ liệu thu chi ngân sách
"""

import streamlit as st
import pandas as pd
import mysql.connector
from sqlalchemy import create_engine, text, inspect
import openai
import json
import os
from typing import Dict, List, Tuple, Optional
import traceback
from dotenv import load_dotenv

# Load biến môi trường từ file .env
load_dotenv()

# ===== CẤU HÌNH CHUNG =====
# Cấu hình MySQL - Ưu tiên biến môi trường, fallback về giá trị mặc định
MYSQL_CONFIG = {
    'host': os.getenv('MYSQL_HOST', '************'),
    'port': int(os.getenv('MYSQL_PORT', 3306)),
    'user': os.getenv('MYSQL_USER', 'etl_qni'),
    'password': os.getenv('MYSQL_PASSWORD', 'passabc'),  # QUAN TRỌNG: Thay đổi password thật
    'database': os.getenv('MYSQL_DATABASE', 'dtm_qni')
}

# Cấu hình OpenAI - Ưu tiên biến môi trường
OPENAI_CONFIG = {
    'api_key': os.getenv('OPENAI_API_KEY', 'sk-xqQGdrYlA13QLWiSWs01VnauXY8uqsWsZaZJe0FnJDNvmEwR'),
    'base_url': os.getenv('OPENAI_BASE_URL', 'https://open.keyai.shop/v1'),
    'model': os.getenv('OPENAI_MODEL', 'gpt-4o')
}

# System prompt cho OpenAI
SYSTEM_PROMPT = """Bạn là trợ lý phân tích dữ liệu thu chi ngân sách, so sánh qua các kỳ dựa trên ngày tháng năm. 
Hãy phân tích dữ liệu một cách chi tiết và đưa ra những nhận xét có ý nghĩa về xu hướng, biến động và các điểm đáng chú ý."""

# ===== KHỞI TẠO STREAMLIT =====
st.set_page_config(
    page_title="Hệ thống Phân tích Thu Chi Ngân sách",
    page_icon="💰",
    layout="wide"
)

# ===== CÁC HÀM TIỆN ÍCH =====

@st.cache_resource
def init_database_connection():
    """Khởi tạo kết nối database với SQLAlchemy"""
    try:
        connection_string = f"mysql+pymysql://{MYSQL_CONFIG['user']}:{MYSQL_CONFIG['password']}@{MYSQL_CONFIG['host']}:{MYSQL_CONFIG['port']}/{MYSQL_CONFIG['database']}"
        engine = create_engine(connection_string, echo=False)
        return engine
    except Exception as e:
        st.error(f"Lỗi kết nối database: {str(e)}")
        return None

def init_openai_client():
    """Khởi tạo OpenAI client"""
    try:
        client = openai.OpenAI(
            api_key=OPENAI_CONFIG['api_key'],
            base_url=OPENAI_CONFIG['base_url']
        )
        return client
    except Exception as e:
        st.error(f"Lỗi khởi tạo OpenAI client: {str(e)}")
        return None

def create_config_table(engine):
    """Tạo bảng config_app nếu chưa tồn tại và thêm dữ liệu mẫu"""
    try:
        with engine.connect() as conn:
            # Tạo bảng config_app
            create_table_sql = """
            CREATE TABLE IF NOT EXISTS config_app (
                id BIGINT AUTO_INCREMENT PRIMARY KEY,
                field VARCHAR(100) NOT NULL,
                query_text TEXT NOT NULL,
                prompt_text TEXT NOT NULL,
                schema_desc TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
            """
            conn.execute(text(create_table_sql))
            
            # Kiểm tra xem đã có dữ liệu mẫu chưa
            check_data = conn.execute(text("SELECT COUNT(*) as count FROM config_app")).fetchone()
            
            if check_data[0] == 0:
                # Thêm dữ liệu mẫu
                sample_data = [
                    {
                        'field': 'thu_chi',
                        'query_text': "SELECT ma_tt, 01_tong_thu_nsnn, 04_tong_chi_nsdp FROM ktxh_thu_chi_ngan_sach LIMIT 5;",
                        'prompt_text': "Tóm tắt tổng thu và chi ngân sách từ dữ liệu này.",
                        'schema_desc': "Table ktxh_thu_chi_ngan_sach: cột id_ky, ma_tt, 01_tong_thu_nsnn (tổng thu NSNN), 04_tong_chi_nsdp (tổng chi)."
                    },
                    {
                        'field': 'join_thu_chi_ky',
                        'query_text': "SELECT t.ma_tt, t.01_tong_thu_nsnn, k.GIA_TRI_NAM, AVG(t.01_tong_thu_nsnn) AS avg_thu FROM ktxh_thu_chi_ngan_sach t JOIN dm_ky k ON t.id_ky = k.ID_KY GROUP BY k.GIA_TRI_NAM LIMIT 5;",
                        'prompt_text': "Phân tích xu hướng thu ngân sách qua các năm, so sánh trung bình theo kỳ.",
                        'schema_desc': "Join giữa ktxh_thu_chi_ngan_sach (thu chi) và dm_ky (kỳ thời gian) để so sánh qua năm."
                    }
                ]
                
                for data in sample_data:
                    insert_sql = """
                    INSERT INTO config_app (field, query_text, prompt_text, schema_desc)
                    VALUES (:field, :query_text, :prompt_text, :schema_desc)
                    """
                    conn.execute(text(insert_sql), data)
                
            conn.commit()
            return True
    except Exception as e:
        st.error(f"Lỗi tạo bảng config: {str(e)}")
        return False

def load_configs_from_db(engine) -> Dict:
    """Load tất cả cấu hình từ database"""
    try:
        with engine.connect() as conn:
            result = conn.execute(text("SELECT * FROM config_app ORDER BY field, id"))
            configs = {}
            for row in result:
                field = row.field
                if field not in configs:
                    configs[field] = []
                configs[field].append({
                    'id': row.id,
                    'query_text': row.query_text,
                    'prompt_text': row.prompt_text,
                    'schema_desc': row.schema_desc
                })
            return configs
    except Exception as e:
        st.error(f"Lỗi load cấu hình: {str(e)}")
        return {}

def execute_safe_query(engine, query: str) -> Optional[pd.DataFrame]:
    """Thực thi query an toàn và trả về DataFrame"""
    try:
        with engine.connect() as conn:
            df = pd.read_sql(text(query), conn)
            return df
    except Exception as e:
        st.error(f"Lỗi thực thi query: {str(e)}")
        return None

def get_database_schema(engine) -> str:
    """Lấy thông tin schema database"""
    try:
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        
        schema_info = "📊 **Schema Database:**\n\n"
        for table in tables[:10]:  # Giới hạn 10 bảng đầu tiên
            columns = inspector.get_columns(table)
            schema_info += f"**{table}:**\n"
            for col in columns[:5]:  # Giới hạn 5 cột đầu tiên
                schema_info += f"  - {col['name']} ({col['type']})\n"
            schema_info += "\n"
        
        return schema_info
    except Exception as e:
        return f"Không thể lấy schema: {str(e)}"

def call_openai_api(client, prompt: str, data: str) -> str:
    """Gọi OpenAI API để phân tích dữ liệu"""
    try:
        full_prompt = f"{prompt}\n\nDữ liệu:\n{data}"
        
        response = client.chat.completions.create(
            model=OPENAI_CONFIG['model'],
            messages=[
                {"role": "system", "content": SYSTEM_PROMPT},
                {"role": "user", "content": full_prompt}
            ],
            max_tokens=1500,
            temperature=0.7
        )
        
        return response.choices[0].message.content
    except Exception as e:
        return f"Lỗi gọi OpenAI API: {str(e)}"

# ===== KHỞI TẠO ỨNG DỤNG =====
def main():
    st.title("💰 Hệ thống Phân tích Thu Chi Ngân sách")
    st.markdown("---")
    
    # Khởi tạo kết nối
    engine = init_database_connection()
    if not engine:
        st.stop()
    
    openai_client = init_openai_client()
    if not openai_client:
        st.stop()
    
    # Tạo bảng config nếu cần
    if not create_config_table(engine):
        st.stop()
    
    # Sidebar menu
    st.sidebar.title("📋 Menu Chính")
    tab_choice = st.sidebar.radio(
        "Chọn chức năng:",
        ["🔍 Query & Chat", "⚙️ Admin Config"]
    )
    
    if tab_choice == "🔍 Query & Chat":
        query_and_chat_tab(engine, openai_client)
    else:
        admin_config_tab(engine)

def query_and_chat_tab(engine, openai_client):
    """Tab Query & Chat"""
    st.header("🔍 Truy vấn và Phân tích dữ liệu")
    
    # Hiển thị schema
    with st.expander("📊 Xem Schema Database"):
        schema_info = get_database_schema(engine)
        st.markdown(schema_info)
    
    # Load cấu hình từ DB
    configs = load_configs_from_db(engine)
    
    if not configs:
        st.warning("Chưa có cấu hình nào. Vui lòng thêm cấu hình trong tab Admin Config.")
        return
    
    # Chọn lĩnh vực
    col1, col2 = st.columns([1, 2])
    
    with col1:
        selected_field = st.selectbox("Chọn lĩnh vực:", list(configs.keys()))
    
    with col2:
        if selected_field and configs[selected_field]:
            config_options = [f"Config {i+1}" for i in range(len(configs[selected_field]))]
            selected_config_idx = st.selectbox("Chọn cấu hình:", range(len(config_options)), format_func=lambda x: config_options[x])
        else:
            selected_config_idx = 0
    
    if selected_field and configs[selected_field]:
        selected_config = configs[selected_field][selected_config_idx]
        
        # Hiển thị thông tin cấu hình
        st.subheader("📝 Thông tin cấu hình")
        
        col1, col2 = st.columns(2)
        with col1:
            st.text_area("Query SQL:", value=selected_config['query_text'], height=100, disabled=True)
        with col2:
            st.text_area("Prompt:", value=selected_config['prompt_text'], height=100, disabled=True)
        
        if selected_config['schema_desc']:
            st.info(f"📋 Mô tả: {selected_config['schema_desc']}")
        
        # Tùy chọn nâng cao
        with st.expander("⚙️ Tùy chọn nâng cao"):
            col1, col2 = st.columns(2)
            with col1:
                custom_query = st.checkbox("Sử dụng query tùy chỉnh")
                if custom_query:
                    custom_query_text = st.text_area("Query SQL tùy chỉnh:", value=selected_config['query_text'], height=100)
                else:
                    custom_query_text = selected_config['query_text']

            with col2:
                custom_prompt = st.checkbox("Sử dụng prompt tùy chỉnh")
                if custom_prompt:
                    custom_prompt_text = st.text_area("Prompt tùy chỉnh:", value=selected_config['prompt_text'], height=100)
                else:
                    custom_prompt_text = selected_config['prompt_text']

        # Nút thực thi
        col1, col2, col3 = st.columns([1, 2, 1])
        with col2:
            if st.button("🚀 Thực thi Query và Phân tích", type="primary", use_container_width=True):
                with st.spinner("Đang thực thi query..."):
                    # Thực thi query
                    df = execute_safe_query(engine, custom_query_text)

                    if df is not None:
                        st.success(f"✅ Query thành công! Tìm thấy {len(df)} dòng dữ liệu.")

                        # Hiển thị thống kê cơ bản
                        col1, col2, col3, col4 = st.columns(4)
                        with col1:
                            st.metric("Số dòng", len(df))
                        with col2:
                            st.metric("Số cột", len(df.columns))
                        with col3:
                            numeric_cols = df.select_dtypes(include=['number']).columns
                            st.metric("Cột số", len(numeric_cols))
                        with col4:
                            null_count = df.isnull().sum().sum()
                            st.metric("Giá trị null", null_count)

                        # Hiển thị dữ liệu
                        st.subheader("📊 Kết quả Query")

                        # Tab hiển thị dữ liệu
                        tab1, tab2, tab3 = st.tabs(["📋 Dữ liệu", "📈 Biểu đồ", "📄 Xuất dữ liệu"])

                        with tab1:
                            st.dataframe(df, use_container_width=True)

                        with tab2:
                            # Tự động tạo biểu đồ cho cột số
                            numeric_cols = df.select_dtypes(include=['number']).columns
                            if len(numeric_cols) > 0:
                                chart_type = st.selectbox("Chọn loại biểu đồ:", ["Line Chart", "Bar Chart", "Area Chart"])
                                selected_col = st.selectbox("Chọn cột để vẽ biểu đồ:", numeric_cols)

                                if chart_type == "Line Chart":
                                    st.line_chart(df[selected_col])
                                elif chart_type == "Bar Chart":
                                    st.bar_chart(df[selected_col])
                                else:
                                    st.area_chart(df[selected_col])
                            else:
                                st.info("Không có cột số để vẽ biểu đồ.")

                        with tab3:
                            # Xuất dữ liệu
                            csv = df.to_csv(index=False)
                            st.download_button(
                                label="📥 Tải xuống CSV",
                                data=csv,
                                file_name=f"data_export_{selected_field}_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.csv",
                                mime="text/csv"
                            )

                            json_data = df.to_json(orient='records', indent=2)
                            st.download_button(
                                label="📥 Tải xuống JSON",
                                data=json_data,
                                file_name=f"data_export_{selected_field}_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.json",
                                mime="application/json"
                            )

                        # Gọi OpenAI API
                        with st.spinner("Đang phân tích dữ liệu với AI..."):
                            data_str = df.to_string(index=False)
                            ai_response = call_openai_api(openai_client, custom_prompt_text, data_str)

                            st.subheader("🤖 Phân tích từ AI")
                            st.markdown(ai_response)

                            # Lưu kết quả phân tích
                            if st.button("💾 Lưu kết quả phân tích"):
                                save_analysis_result(engine, selected_field, custom_query_text, ai_response)

def save_analysis_result(engine, field: str, query: str, analysis: str):
    """Lưu kết quả phân tích vào database"""
    try:
        with engine.connect() as conn:
            # Tạo bảng lưu kết quả nếu chưa có
            create_results_table = """
            CREATE TABLE IF NOT EXISTS analysis_results (
                id BIGINT AUTO_INCREMENT PRIMARY KEY,
                field VARCHAR(100),
                query_text TEXT,
                analysis_result TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """
            conn.execute(text(create_results_table))

            # Lưu kết quả
            insert_sql = """
            INSERT INTO analysis_results (field, query_text, analysis_result)
            VALUES (:field, :query_text, :analysis_result)
            """
            conn.execute(text(insert_sql), {
                'field': field,
                'query_text': query,
                'analysis_result': analysis
            })
            conn.commit()
            st.success("✅ Đã lưu kết quả phân tích!")
    except Exception as e:
        st.error(f"❌ Lỗi lưu kết quả: {str(e)}")

def admin_config_tab(engine):
    """Tab Admin Config"""
    st.header("⚙️ Quản lý Cấu hình")
    
    # Tabs con
    sub_tab1, sub_tab2 = st.tabs(["➕ Thêm Cấu hình", "📋 Danh sách Cấu hình"])
    
    with sub_tab1:
        add_config_form(engine)
    
    with sub_tab2:
        list_configs(engine)

def add_config_form(engine):
    """Form thêm cấu hình mới"""
    st.subheader("➕ Thêm Cấu hình Mới")
    
    with st.form("add_config_form"):
        field = st.text_input("Tên lĩnh vực:", placeholder="Ví dụ: sales, marketing, finance")
        query_text = st.text_area("Query SQL:", height=150, placeholder="SELECT * FROM table_name WHERE condition;")
        prompt_text = st.text_area("Prompt cho AI:", height=100, placeholder="Phân tích dữ liệu này và đưa ra nhận xét...")
        schema_desc = st.text_area("Mô tả Schema/Table:", height=80, placeholder="Mô tả về bảng, cột và ý nghĩa dữ liệu...")
        
        submitted = st.form_submit_button("💾 Lưu Cấu hình", type="primary")
        
        if submitted:
            if field and query_text and prompt_text:
                try:
                    with engine.connect() as conn:
                        insert_sql = """
                        INSERT INTO config_app (field, query_text, prompt_text, schema_desc)
                        VALUES (:field, :query_text, :prompt_text, :schema_desc)
                        """
                        conn.execute(text(insert_sql), {
                            'field': field,
                            'query_text': query_text,
                            'prompt_text': prompt_text,
                            'schema_desc': schema_desc
                        })
                        conn.commit()
                        st.success("✅ Đã lưu cấu hình thành công!")
                        st.rerun()
                except Exception as e:
                    st.error(f"❌ Lỗi lưu cấu hình: {str(e)}")
            else:
                st.error("❌ Vui lòng điền đầy đủ thông tin bắt buộc!")

def list_configs(engine):
    """Hiển thị danh sách cấu hình"""
    st.subheader("📋 Danh sách Cấu hình Hiện tại")

    try:
        with engine.connect() as conn:
            df = pd.read_sql(text("SELECT * FROM config_app ORDER BY field, id"), conn)

            if len(df) > 0:
                # Thống kê tổng quan
                st.info(f"📊 Tổng cộng: {len(df)} cấu hình trong {df['field'].nunique()} lĩnh vực")

                # Hiển thị bảng với khả năng chỉnh sửa
                for idx, row in df.iterrows():
                    with st.expander(f"🔧 {row['field']} (ID: {row['id']}) - {row['created_at'].strftime('%d/%m/%Y %H:%M') if row['created_at'] else 'N/A'}"):
                        col1, col2 = st.columns([3, 1])

                        with col1:
                            st.markdown(f"**Query SQL:**")
                            st.code(row['query_text'], language='sql')

                            st.markdown(f"**Prompt AI:**")
                            st.write(row['prompt_text'])

                            if row['schema_desc']:
                                st.markdown(f"**Mô tả Schema:**")
                                st.write(row['schema_desc'])

                        with col2:
                            st.markdown("**Thao tác:**")
                            if st.button(f"✏️ Sửa", key=f"edit_{row['id']}"):
                                st.session_state[f"edit_mode_{row['id']}"] = True
                                st.rerun()

                            if st.button(f"🗑️ Xóa", key=f"delete_{row['id']}", type="secondary"):
                                if st.session_state.get(f"confirm_delete_{row['id']}", False):
                                    delete_config(engine, row['id'])
                                    st.session_state[f"confirm_delete_{row['id']}"] = False
                                    st.rerun()
                                else:
                                    st.session_state[f"confirm_delete_{row['id']}"] = True
                                    st.warning("Click lại để xác nhận xóa!")

                        # Form chỉnh sửa nếu được kích hoạt
                        if st.session_state.get(f"edit_mode_{row['id']}", False):
                            edit_config_form(engine, row)
            else:
                st.info("Chưa có cấu hình nào.")

    except Exception as e:
        st.error(f"Lỗi load danh sách cấu hình: {str(e)}")

def edit_config_form(engine, config_row):
    """Form chỉnh sửa cấu hình"""
    st.markdown("---")
    st.subheader(f"✏️ Chỉnh sửa cấu hình ID: {config_row['id']}")

    with st.form(f"edit_config_form_{config_row['id']}"):
        new_field = st.text_input("Tên lĩnh vực:", value=config_row['field'])
        new_query = st.text_area("Query SQL:", value=config_row['query_text'], height=150)
        new_prompt = st.text_area("Prompt cho AI:", value=config_row['prompt_text'], height=100)
        new_schema_desc = st.text_area("Mô tả Schema:", value=config_row['schema_desc'] or "", height=80)

        col1, col2 = st.columns(2)
        with col1:
            if st.form_submit_button("💾 Cập nhật", type="primary"):
                update_config(engine, config_row['id'], new_field, new_query, new_prompt, new_schema_desc)
                st.session_state[f"edit_mode_{config_row['id']}"] = False
                st.rerun()

        with col2:
            if st.form_submit_button("❌ Hủy"):
                st.session_state[f"edit_mode_{config_row['id']}"] = False
                st.rerun()

def update_config(engine, config_id: int, field: str, query_text: str, prompt_text: str, schema_desc: str):
    """Cập nhật cấu hình"""
    try:
        with engine.connect() as conn:
            update_sql = """
            UPDATE config_app
            SET field = :field, query_text = :query_text, prompt_text = :prompt_text, schema_desc = :schema_desc
            WHERE id = :id
            """
            conn.execute(text(update_sql), {
                'id': config_id,
                'field': field,
                'query_text': query_text,
                'prompt_text': prompt_text,
                'schema_desc': schema_desc
            })
            conn.commit()
            st.success("✅ Đã cập nhật cấu hình thành công!")
    except Exception as e:
        st.error(f"❌ Lỗi cập nhật cấu hình: {str(e)}")

def delete_config(engine, config_id: int):
    """Xóa cấu hình"""
    try:
        with engine.connect() as conn:
            conn.execute(text("DELETE FROM config_app WHERE id = :id"), {'id': config_id})
            conn.commit()
            st.success("✅ Đã xóa cấu hình thành công!")
    except Exception as e:
        st.error(f"❌ Lỗi xóa cấu hình: {str(e)}")

if __name__ == "__main__":
    main()
