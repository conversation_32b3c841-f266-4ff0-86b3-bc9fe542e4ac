# 💰 Hệ thống Phân tích Thu Chi Ngân sách

Ứng dụng web Streamlit tích hợp OpenAI API và MySQL để phân tích dữ liệu thu chi ngân sách với khả năng quản lý cấu hình động.

## 🚀 Tính năng chính

### 1. <PERSON>uản lý Cấu hình Động
- ✅ Thêm/sửa/xóa lĩnh vực mới qua giao diện web
- ✅ Tạo query SQL tùy chỉnh (hỗ trợ multi-table và JOIN)
- ✅ Thiết lập prompt AI cho từng loại phân tích
- ✅ Lưu trữ cấu hình trong database MySQL

### 2. Phân tích Dữ liệu Thông minh
- ✅ Thực thi query SQL an toàn với parameterized queries
- ✅ Tích hợp OpenAI GPT-4o để phân tích dữ liệu
- ✅ Hiển thị kết quả trực quan với Streamlit
- ✅ Hỗ trợ phân tích xu hướng qua các kỳ/năm

### 3. Giao diện Thân thiện
- ✅ Menu sidebar với 2 tab chính: "Query & Chat" và "Admin Config"
- ✅ Form nhập liệu trực quan
- ✅ Hiển thị schema database
- ✅ Xử lý lỗi với thông báo rõ ràng

## 📋 Yêu cầu hệ thống

- Python 3.8+
- MySQL Server
- Kết nối internet (để gọi OpenAI API)

## 🛠️ Cài đặt

### 1. Clone repository
```bash
git clone <repository-url>
cd <project-folder>
```

### 2. Cài đặt dependencies
```bash
pip install -r requirements.txt
```

### 3. Cấu hình Database và API

**⚠️ QUAN TRỌNG: Cập nhật thông tin kết nối**

Mở file `app.py` và cập nhật:

```python
# Cấu hình MySQL - THAY ĐỔI PASSWORD THẬT
MYSQL_CONFIG = {
    'host': '************',
    'port': 3306,
    'user': 'etl_qni',
    'password': 'YOUR_REAL_PASSWORD_HERE',  # ⚠️ Thay password thật
    'database': 'dtm_qni'
}

# Cấu hình OpenAI - THAY ĐỔI API KEY THẬT
OPENAI_CONFIG = {
    'api_key': 'YOUR_OPENAI_API_KEY_HERE',  # ⚠️ Thay API key thật
    'base_url': 'https://open.keyai.shop/v1',
    'model': 'gpt-4o'
}
```

### 4. Chạy ứng dụng
```bash
streamlit run app.py
```

Ứng dụng sẽ chạy tại: `http://localhost:8501`

## 📊 Cấu trúc Database

### Bảng chính (có sẵn)
- `ktxh_thu_chi_ngan_sach`: Dữ liệu thu chi ngân sách
- `dm_ky`: Thông tin kỳ thời gian

### Bảng cấu hình (tự động tạo)
```sql
CREATE TABLE config_app (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    field VARCHAR(100) NOT NULL,           -- Tên lĩnh vực
    query_text TEXT NOT NULL,              -- Query SQL
    prompt_text TEXT NOT NULL,             -- Prompt cho AI
    schema_desc TEXT,                      -- Mô tả schema/table
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## 🎯 Hướng dẫn sử dụng

### Tab "Query & Chat"
1. **Xem Schema**: Click "Xem Schema Database" để hiểu cấu trúc dữ liệu
2. **Chọn lĩnh vực**: Dropdown menu load động từ database
3. **Chọn cấu hình**: Mỗi lĩnh vực có thể có nhiều cấu hình khác nhau
4. **Thực thi**: Click "Thực thi Query và Phân tích" để chạy

### Tab "Admin Config"
#### Thêm cấu hình mới:
1. **Tên lĩnh vực**: Ví dụ "sales", "marketing", "finance"
2. **Query SQL**: Viết query với JOIN nếu cần
3. **Prompt AI**: Mô tả cách phân tích dữ liệu
4. **Mô tả Schema**: Giải thích ý nghĩa các bảng/cột

#### Quản lý cấu hình:
- Xem danh sách tất cả cấu hình
- Xóa cấu hình không cần thiết
- Cấu hình được cập nhật real-time

## 📝 Ví dụ cấu hình mẫu

### 1. Thu Chi Cơ bản
```sql
-- Query
SELECT ma_tt, 01_tong_thu_nsnn, 04_tong_chi_nsdp 
FROM ktxh_thu_chi_ngan_sach 
LIMIT 5;

-- Prompt
Tóm tắt tổng thu và chi ngân sách từ dữ liệu này.
```

### 2. Phân tích theo năm (JOIN)
```sql
-- Query
SELECT t.ma_tt, t.01_tong_thu_nsnn, k.GIA_TRI_NAM, 
       AVG(t.01_tong_thu_nsnn) AS avg_thu 
FROM ktxh_thu_chi_ngan_sach t 
JOIN dm_ky k ON t.id_ky = k.ID_KY 
GROUP BY k.GIA_TRI_NAM 
LIMIT 5;

-- Prompt
Phân tích xu hướng thu ngân sách qua các năm, so sánh trung bình theo kỳ.
```

## 🔒 Bảo mật

- ✅ Sử dụng parameterized queries để tránh SQL injection
- ✅ Kết nối database qua SQLAlchemy engine
- ⚠️ **Lưu ý**: Thay đổi password và API key thật trong production
- ⚠️ **Khuyến nghị**: Sử dụng biến môi trường cho thông tin nhạy cảm

## 🚀 Mở rộng

### Thêm lĩnh vực mới
1. Vào tab "Admin Config"
2. Điền form "Thêm Cấu hình Mới"
3. Lưu và sử dụng ngay lập tức

### Thêm table mới
1. Tạo query JOIN với table mới
2. Viết prompt phù hợp
3. Mô tả schema để AI hiểu rõ dữ liệu

## 🐛 Xử lý lỗi

- **Lỗi kết nối DB**: Kiểm tra thông tin MYSQL_CONFIG
- **Lỗi OpenAI API**: Kiểm tra API key và base_url
- **Lỗi SQL**: Kiểm tra syntax query trong form admin
- **Lỗi hiển thị**: Refresh trang hoặc restart ứng dụng

## 📞 Hỗ trợ

Nếu gặp vấn đề, vui lòng:
1. Kiểm tra log trong terminal
2. Xem thông báo lỗi trên giao diện
3. Đảm bảo database và API hoạt động bình thường

---

**Phát triển bởi**: AI Assistant  
**Phiên bản**: 1.0  
**Cập nhật**: 2025-08-21
