#!/usr/bin/env python3
"""
Script khởi chạy ứng dụng Streamlit
Kiểm tra dependencies và khởi chạy app
"""

import subprocess
import sys
import os
from pathlib import Path

def check_python_version():
    """Kiểm tra phiên bản Python"""
    if sys.version_info < (3, 8):
        print("❌ Yêu cầu Python 3.8 trở lên!")
        print(f"Phiên bản hiện tại: {sys.version}")
        return False
    print(f"✅ Python version: {sys.version}")
    return True

def install_requirements():
    """Cài đặt requirements nếu cần"""
    requirements_file = Path("requirements.txt")
    if not requirements_file.exists():
        print("❌ Không tìm thấy file requirements.txt")
        return False
    
    try:
        print("📦 Đang cài đặt dependencies...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Đã cài đặt dependencies thành công!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Lỗi cài đặt dependencies: {e}")
        return False

def check_env_file():
    """Kiểm tra file .env"""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists():
        if env_example.exists():
            print("⚠️  File .env không tồn tại.")
            print("📋 Vui lòng copy .env.example thành .env và cập nhật thông tin:")
            print("   cp .env.example .env")
            print("   # Sau đó chỉnh sửa .env với thông tin thật")
        else:
            print("⚠️  Không tìm thấy file .env hoặc .env.example")
        return False
    
    print("✅ File .env đã tồn tại")
    return True

def run_streamlit():
    """Chạy ứng dụng Streamlit"""
    try:
        print("🚀 Đang khởi chạy ứng dụng Streamlit...")
        print("📱 Ứng dụng sẽ mở tại: http://localhost:8501")
        print("🛑 Nhấn Ctrl+C để dừng ứng dụng")
        print("-" * 50)
        
        subprocess.run([sys.executable, "-m", "streamlit", "run", "app.py"])
    except KeyboardInterrupt:
        print("\n👋 Đã dừng ứng dụng!")
    except subprocess.CalledProcessError as e:
        print(f"❌ Lỗi chạy Streamlit: {e}")
    except FileNotFoundError:
        print("❌ Không tìm thấy Streamlit. Vui lòng cài đặt: pip install streamlit")

def main():
    """Hàm chính"""
    print("=" * 60)
    print("🏦 HỆ THỐNG PHÂN TÍCH THU CHI NGÂN SÁCH")
    print("=" * 60)
    
    # Kiểm tra Python version
    if not check_python_version():
        return
    
    # Kiểm tra và cài đặt dependencies
    install_choice = input("\n📦 Bạn có muốn cài đặt/cập nhật dependencies? (y/n): ").lower()
    if install_choice in ['y', 'yes', '']:
        if not install_requirements():
            return
    
    # Kiểm tra file .env
    if not check_env_file():
        continue_choice = input("\n⚠️  Bạn có muốn tiếp tục mà không có file .env? (y/n): ").lower()
        if continue_choice not in ['y', 'yes']:
            print("👋 Vui lòng tạo file .env trước khi chạy ứng dụng!")
            return
    
    # Chạy ứng dụng
    print("\n" + "=" * 60)
    run_streamlit()

if __name__ == "__main__":
    main()
